#!/usr/bin/env python
"""
Test script for support user detail view access
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mcshop.settings')
django.setup()

from django.contrib.auth.models import User
from shop.models import UserProfile, Purchase, MinecraftServer, Category, Item, PurchaseItem
from django.test import RequestFactory
from django.contrib.admin.sites import AdminSite
from shop.admin import PurchaseAdmin


def test_detail_view_access():
    """Test support user detail view access"""
    print("🧪 Testing Support User Detail View Access")
    print("=" * 50)
    
    # Create support user
    support_user = User.objects.create_user(
        username='support_detail_test',
        email='<EMAIL>',
        password='testpass123',
        is_staff=True
    )
    
    # Set support flag
    support_user.profile.is_support = True
    support_user.profile.save()
    
    print(f"✅ Created support user: {support_user.username}")
    
    # Create test data
    server = MinecraftServer.objects.create(
        name="test_server_detail",
        ip="127.0.0.1",
        port=25565
    )
    
    category = Category.objects.create(
        name="test_category_detail"
    )
    
    item = Item.objects.create(
        name="test_item_detail",
        category=category,
        minecraft_server=server,
        price=1000
    )
    
    # Create test purchase
    purchase = Purchase.objects.create(
        minecraft_username="test_detail_access",
        state=Purchase.State.SUCCESSFUL
    )
    purchase.ref_id = Purchase.generate_reference_id()
    purchase.save()
    
    PurchaseItem.objects.create(
        purchase=purchase,
        item=item,
        quantity=1
    )
    
    print(f"✅ Created test purchase with ID: {purchase.id}, ref_id: {purchase.ref_id}")
    
    # Test admin access
    factory = RequestFactory()
    admin_site = AdminSite()
    purchase_admin = PurchaseAdmin(Purchase, admin_site)
    
    print("\n🔍 Testing Different URL Patterns:")
    
    # Test list view (should be empty without search)
    list_request = factory.get('/admin/shop/purchase/')
    list_request.user = support_user
    list_queryset = purchase_admin.get_queryset(list_request)
    print(f"✅ List view (no search): {list_queryset.count()} purchases (should be 0)")
    
    # Test list view with search
    search_request = factory.get(f'/admin/shop/purchase/?q={purchase.ref_id}')
    search_request.user = support_user
    search_queryset = purchase_admin.get_queryset(search_request)
    print(f"✅ List view (with search): {search_queryset.count()} purchases (should be 1)")
    
    # Test detail view (change page)
    detail_request = factory.get(f'/admin/shop/purchase/{purchase.id}/change/')
    detail_request.user = support_user
    detail_queryset = purchase_admin.get_queryset(detail_request)
    print(f"✅ Detail view (change): {detail_queryset.count()} purchases (should be 1)")
    
    # Test detail view (delete page)
    delete_request = factory.get(f'/admin/shop/purchase/{purchase.id}/delete/')
    delete_request.user = support_user
    delete_queryset = purchase_admin.get_queryset(delete_request)
    print(f"✅ Detail view (delete): {delete_queryset.count()} purchases (should be 1)")
    
    # Verify the correct purchase is returned
    if detail_queryset.exists():
        returned_purchase = detail_queryset.first()
        print(f"✅ Correct purchase returned: {returned_purchase.id == purchase.id}")
        print(f"   Purchase ref_id: {returned_purchase.ref_id}")
    
    print("\n🎉 Detail view access test completed!")
    print("\nSummary:")
    print("- Support users can access detail view directly via URL")
    print("- List view still requires search to show purchases")
    print("- Detail view access works for both change and delete URLs")


if __name__ == '__main__':
    test_detail_view_access()
