"""
Alternative approach: Monkey patching User model to add is_support field directly

This is NOT recommended for new projects, but can work for existing ones.
The UserProfile approach we implemented is cleaner and safer.
"""

from django.contrib.auth.models import User
from django.db import models


def add_is_support_to_user():
    """
    Monkey patch the User model to add is_support field
    
    This would need to be called in apps.py ready() method
    and requires a database migration to add the column.
    """
    
    # Add the field to the User model
    User.add_to_class(
        'is_support',
        models.BooleanField(
            default=False,
            help_text="Support users can search purchases by reference ID and run failed commands"
        )
    )
    
    # Add a property to check support status
    def is_support_user(self):
        return getattr(self, 'is_support', False)
    
    User.add_to_class('is_support_user', property(is_support_user))


# Example of how this would be used in apps.py:
"""
from django.apps import AppConfig

class ShopConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'shop'

    def ready(self):
        # Import signals when the app is ready
        import shop.signals
        
        # Add is_support field to User model
        from .alternative_user_extension import add_is_support_to_user
        add_is_support_to_user()
"""

# Then you would need a migration like:
"""
from django.db import migrations, models

class Migration(migrations.Migration):
    dependencies = [
        ('shop', '0026_create_user_profiles_for_existing_users'),
    ]

    operations = [
        migrations.RunSQL(
            "ALTER TABLE auth_user ADD COLUMN is_support BOOLEAN NOT NULL DEFAULT FALSE;",
            reverse_sql="ALTER TABLE auth_user DROP COLUMN is_support;"
        ),
    ]
"""

print("This file shows an alternative approach using monkey patching.")
print("The UserProfile approach we implemented is cleaner and safer.")
print("For new projects, use a custom User model from the start.")
