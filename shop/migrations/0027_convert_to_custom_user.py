# Generated manually for custom user model conversion

from django.db import migrations, models


def transfer_support_data(apps, schema_editor):
    """Transfer is_support data from UserProfile to auth_user table"""
    # Get the UserProfile model from the previous state
    UserProfile = apps.get_model('shop', 'UserProfile')
    
    # Use raw SQL to update the auth_user table
    db_alias = schema_editor.connection.alias
    
    # Get all UserProfile records with is_support=True
    support_profiles = UserProfile.objects.using(db_alias).filter(is_support=True)
    
    # Update the auth_user table directly
    for profile in support_profiles:
        schema_editor.execute(
            "UPDATE auth_user SET is_support = %s WHERE id = %s",
            [True, profile.user_id]
        )


def reverse_transfer_support_data(apps, schema_editor):
    """Reverse operation - recreate UserProfile records"""
    # This is a simplified reverse - in practice you might want to recreate UserProfile
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
        ('shop', '0026_create_user_profiles_for_existing_users'),
    ]

    operations = [
        # Add is_support field to auth_user table
        migrations.RunSQL(
            "ALTER TABLE auth_user ADD COLUMN is_support BOOLEAN NOT NULL DEFAULT FALSE;",
            reverse_sql="ALTER TABLE auth_user DROP COLUMN is_support;"
        ),
        
        # Transfer data from UserProfile to auth_user
        migrations.RunPython(transfer_support_data, reverse_transfer_support_data),
        
        # Remove UserProfile table
        migrations.RunSQL(
            "DROP TABLE shop_userprofile;",
            reverse_sql="CREATE TABLE shop_userprofile (id SERIAL PRIMARY KEY, user_id INTEGER UNIQUE REFERENCES auth_user(id), is_support BOOLEAN NOT NULL DEFAULT FALSE);"
        ),
    ]
