import json
import random

from django.db import models
from django.contrib.auth.models import User
from .storage import ItemImageStorage, ContentCreatorImageStorage


# Create your models here.
class UserProfile(models.Model):
    """Extended user profile with additional fields"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    is_support = models.BooleanField(
        default=False,
        help_text="Support users can search purchases by reference ID and run failed commands"
    )

    def __str__(self):
        return f"{self.user.username} - Profile"

    class Meta:
        verbose_name = "User Profile"
        verbose_name_plural = "User Profiles"


class MinecraftServer(models.Model):
    name = models.CharField(max_length=100)
    display_name = models.CharField(max_length=100, null=True, blank=True)
    ip = models.GenericIPAddressField()
    port = models.PositiveIntegerField()
    domain = models.CharField(max_length=255, null=True, blank=True)
    rcon_port = models.PositiveIntegerField(null=True, blank=True)
    rcon_password = models.CharField(max_length=255, blank=True, null=True, help_text="RCON password for this server")
    api_port = models.PositiveIntegerField(null=True, blank=True, help_text="API port for username validation")
    api_token = models.CharField(max_length=255, blank=True, null=True, help_text="API token for username validation")
    proxy = models.BooleanField(default=False)
    enabled = models.BooleanField(default=True)
    published = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.name} ({self.ip}:{self.port})"



class Platform(models.Model):
    """
    Represents a platform (e.g., Windows, Android, Mac, Linux)
    """
    name = models.CharField(max_length=100, unique=True)
    display_name = models.CharField(max_length=150, blank=True, null=True)
    help = models.TextField(blank=True, null=True, help_text="Markdown help text for this platform")
    download_url = models.URLField(max_length=500, blank=True, null=True, help_text="Direct download URL for this platform")
    image_url = models.URLField(max_length=500, blank=True, null=True, help_text="Platform icon image URL")
    order = models.PositiveIntegerField(default=0, help_text="Order for sorting (lower values appear first)")
    enabled = models.BooleanField(default=True)
    published = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order', 'name']

    def __str__(self):
        return self.display_name or self.name


class Launcher(models.Model):
    """
    Represents a launcher within a platform (e.g., TLauncher, PojavLauncher)
    """
    name = models.CharField(max_length=100)
    display_name = models.CharField(max_length=150, blank=True, null=True)
    help = models.TextField(blank=True, null=True, help_text="Markdown help text for this launcher")
    download_url = models.URLField(max_length=500, blank=True, null=True, help_text="Direct download URL for this launcher")
    image_url = models.URLField(max_length=500, blank=True, null=True, help_text="Launcher icon image URL")
    platform = models.ForeignKey(Platform, on_delete=models.CASCADE, related_name='launchers')
    order = models.PositiveIntegerField(default=0, help_text="Order for sorting (lower values appear first)")
    enabled = models.BooleanField(default=True)
    published = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order', 'name']
        unique_together = ('platform', 'name')

    def __str__(self):
        return f"{self.platform.name} - {self.display_name or self.name}"


class Version(models.Model):
    """
    Represents a version within a launcher (e.g., 1.21.4, 1.21.5)
    """
    name = models.CharField(max_length=100)
    display_name = models.CharField(max_length=150, blank=True, null=True)
    help = models.TextField(blank=True, null=True, help_text="Markdown help text for this version")
    download_url = models.URLField(max_length=500, blank=True, null=True, help_text="Direct download URL for this version")
    image_url = models.URLField(max_length=500, blank=True, null=True, help_text="Version icon image URL")
    launcher = models.ForeignKey(Launcher, on_delete=models.CASCADE, related_name='versions')
    order = models.PositiveIntegerField(default=0, help_text="Order for sorting (lower values appear first)")
    enabled = models.BooleanField(default=True)
    published = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order', 'name']
        unique_together = ('launcher', 'name')

    def __str__(self):
        return f"{self.launcher.platform.name} - {self.launcher.name} - {self.display_name or self.name}"


class Category(models.Model):
    name = models.CharField(max_length=100)
    display_name = models.CharField(max_length=150, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    order = models.PositiveIntegerField(default=0, help_text="Order for sorting (lower values appear first)")
    enabled = models.BooleanField(default=True)
    published = models.BooleanField(default=True)

    class Meta:
        verbose_name_plural = "Categories"
        ordering = ['order', 'name']

    def __str__(self):
        return self.name


class ContentCreator(models.Model):
    name = models.CharField(max_length=100)
    display_name = models.CharField(max_length=150, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    image = models.ImageField(storage=ContentCreatorImageStorage(), null=True, blank=True)
    commission_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=20.00,
        help_text="Commission rate percentage (e.g., 20.00 for 20%)"
    )
    admin_notes = models.TextField(
        blank=True,
        null=True,
        help_text="Internal admin notes"
    )
    order = models.PositiveIntegerField(default=0, help_text="Order for sorting (lower values appear first)")
    enabled = models.BooleanField(default=True)
    published = models.BooleanField(default=True)

    class Meta:
        ordering = ['order', 'name']

    def __str__(self):
        return self.name

    @property
    def total_earned(self):
        """Sum of all profit shares from purchases where this ContentCreator was the referrer"""
        from django.db.models import Sum
        result = self.referred_purchases.filter(
            referral_commission__isnull=False
        ).aggregate(total=Sum('referral_commission'))
        return result['total'] or 0

    @property
    def total_settled(self):
        """Sum of all settled profit shares"""
        from django.db.models import Sum
        result = self.referred_purchases.filter(
            referral_settlement_status='settled',
            referral_commission__isnull=False
        ).aggregate(total=Sum('referral_commission'))
        return result['total'] or 0

    @property
    def total_unsettled(self):
        """Sum of all unsettled profit shares (total_earned - total_settled)"""
        return self.total_earned - self.total_settled


class Item(models.Model):
    name = models.CharField(max_length=100)
    display_name = models.CharField(max_length=150, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    image = models.ImageField(storage=ItemImageStorage(), null=True, blank=True)
    category = models.ForeignKey(Category, on_delete=models.SET_NULL, null=True, blank=True)
    price = models.IntegerField(default=0)
    ucoin_price = models.PositiveIntegerField(default=0)
    commands = models.TextField(blank=True, null=True, help_text="Use ',' to separate multiple commands")
    revoke_commands = models.TextField(blank=True, null=True, help_text="Use ',' to separate multiple revoke commands")
    minecraft_server = models.ForeignKey(MinecraftServer, on_delete=models.SET_NULL, null=True, blank=True)
    expiration_days = models.PositiveIntegerField(null=True, blank=True)
    require_user_verification = models.BooleanField(default=False, help_text="Verify user presence on server before executing commands")
    order = models.PositiveIntegerField(default=0, help_text="Order for sorting (lower values appear first)")
    enabled = models.BooleanField(default=True)
    published = models.BooleanField(default=True)

    class Meta:
        ordering = ['order', 'name']

    def __str__(self):
        return self.name


    @property
    def is_one_time(self):
        return self.expiration_days == 0 or not self.expiration_days


class Purchase(models.Model):
    class State(models.TextChoices):
        CREATED = "created"
        SUCCESSFUL = "successful"
        FAILED = "failed"
        COMMAND_FAILED = "command_failed"
        FINISHED = "finished"
        TIMEOUT = "timeout"

    class SubscriptionStatus(models.TextChoices):
        ONETIME = 'onetime'
        SUBSCRIBED = 'subscribed'
        EXPIRED = 'expired'
        FAILED_REVOKE = 'failed_revoke'

    class ReferralSettlementStatus(models.TextChoices):
        PENDING = 'pending', 'Pending'
        SETTLED = 'settled', 'Settled'

    minecraft_username = models.CharField(max_length=100)
    mobile_number = models.CharField(max_length=15, blank=True, null=True)
    items = models.ManyToManyField(Item, through='PurchaseItem', related_name='purchases')
    state = models.CharField(max_length=20, choices=State.choices, default=State.CREATED)
    referrer = models.ForeignKey(ContentCreator, null=True, blank=True, on_delete=models.SET_NULL, related_name='referred_purchases')
    referral_commission = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Calculated profit share amount for the referrer"
    )
    referral_settlement_status = models.CharField(
        max_length=20,
        choices=ReferralSettlementStatus.choices,
        null=True,
        blank=True,
        help_text="Settlement status for referral commission"
    )
    referral_settled_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When the referral commission was settled"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    payment_succeeded_at = models.DateTimeField(null=True, blank=True)
    ref_id = models.CharField(max_length=8, unique=True, blank=True, null=True, help_text="8-digit human-readable reference ID")
    # Zarinpal fields
    authority = models.CharField(max_length=100, blank=True, null=True, help_text="Zarinpal authority code")
    zarinpal_ref_id = models.CharField(max_length=100, blank=True, null=True, help_text="Zarinpal reference ID")
    zarinpal_verify_response = models.TextField(blank=True, null=True, help_text="Complete Zarinpal verify response as JSON")
    zarinpal_code = models.IntegerField(blank=True, null=True, help_text="Zarinpal verification code (100, 101, etc.)")

    def __str__(self):
        item_count = self.purchase_items.count()
        if item_count == 1:
            first_item = self.purchase_items.first()
            return f"{self.ref_id} - {self.minecraft_username} - {first_item.item.name} [{self.state}]"
        else:
            return f"{self.ref_id} - {self.minecraft_username} - {item_count} items [{self.state}]"

    def get_total_price(self):
        """Calculate total price for all items in this purchase"""
        total = 0
        for purchase_item in self.purchase_items.all():
            total += purchase_item.item.price * purchase_item.quantity
        return total

    def get_items_description(self):
        """Get a description of all items for payment description"""
        items = []
        for purchase_item in self.purchase_items.all():
            item_name = purchase_item.item.display_name or purchase_item.item.name
            if purchase_item.quantity > 1:
                items.append(f"{item_name} x{purchase_item.quantity}")
            else:
                items.append(item_name)

        if len(items) == 1:
            return f"Purchase of {items[0]}"
        elif len(items) <= 3:
            return f"Purchase of {', '.join(items)}"
        else:
            return f"Purchase of {len(items)} items"

    @classmethod
    def generate_reference_id(cls):
        """Generate a unique 8-digit human-readable reference ID"""
        # Use characters that are easy to read and distinguish (avoid 0, O, 1, I)
        # chars = '23456789ABCDEFGHJKLMNPQRSTUVWXYZ'
        chars = '1234567890'

        while True:
            # Generate 8-character reference ID
            reference_id = ''.join(random.choices(chars, k=8))

            # Check if it's unique
            if not cls.objects.filter(ref_id=reference_id).exists():
                return reference_id

    def save_zarinpal_verify_response(self, response_data):
        """Save Zarinpal verify response and extract code"""
        if response_data:
            self.zarinpal_verify_response = json.dumps(response_data)
            self.zarinpal_code = response_data.get('code')
            self.save()

    def get_derived_state_from_commands(self):
        """
        Derive purchase state from the command execution states of all purchase items.
        Returns the appropriate state based on command execution progress.
        """
        purchase_items = self.purchase_items.all()
        if not purchase_items.exists():
            return self.state  # Return current state if no items

        command_states = [item.get_command_execution_state() for item in purchase_items]

        # If any commands failed, purchase is COMMAND_FAILED
        if 'failed' in command_states:
            return self.State.COMMAND_FAILED

        # If any commands are running or pending, purchase remains SUCCESSFUL (in progress)
        if 'running' in command_states or 'pending' in command_states:
            return self.State.SUCCESSFUL

        # If all commands are successful, purchase is FINISHED
        if all(state == 'successful' for state in command_states):
            return self.State.FINISHED

        # Default fallback
        return self.state

    def update_state_from_commands(self):
        """Update the purchase state based on command execution states and save."""
        new_state = self.get_derived_state_from_commands()
        if new_state != self.state:
            self.state = new_state
            self.save()

    def calculate_referral_commission(self):
        """Calculate and set referral commission when purchase is completed"""
        if self.referrer and self.payment_succeeded_at and not self.referral_commission:
            from decimal import Decimal
            total_amount = Decimal(str(self.get_total_price()))
            commission_rate = self.referrer.commission_rate
            commission_amount = (total_amount * commission_rate) / Decimal('100')

            self.referral_commission = commission_amount
            self.referral_settlement_status = self.ReferralSettlementStatus.PENDING
            # Note: referral_settled_at remains None until manually settled

    def calculate_and_set_referral_commission(self):
        """Calculate and set referral commission, then save the purchase"""
        self.calculate_referral_commission()
        self.save()

    def recalculate_referral_commission(self, force=False):
        """Recalculate referral commission, optionally forcing recalculation even if already set"""
        if self.referrer and self.payment_succeeded_at:
            if force or not self.referral_commission:
                from decimal import Decimal
                total_amount = Decimal(str(self.get_total_price()))
                commission_rate = self.referrer.commission_rate
                commission_amount = (total_amount * commission_rate) / Decimal('100').quantize(Decimal('0.01'))

                self.referral_commission = commission_amount
                if not self.referral_settlement_status:
                    self.referral_settlement_status = self.ReferralSettlementStatus.PENDING
                # Don't change settlement status if already settled, unless forced (commented out, not needed)
                # if force and self.referral_settlement_status == self.ReferralSettlementStatus.SETTLED:
                #     self.referral_settlement_status = self.ReferralSettlementStatus.PENDING
                #     self.referral_settled_at = None

    def save(self, *args, **kwargs):
        # Generate reference_id if not set
        if not self.ref_id:
            self.ref_id = self.generate_reference_id()
        super().save(*args, **kwargs)


class PurchaseItem(models.Model):
    """Through model for Purchase-Item many-to-many relationship"""
    purchase = models.ForeignKey(Purchase, on_delete=models.CASCADE, related_name='purchase_items')
    item = models.ForeignKey(Item, on_delete=models.PROTECT)
    quantity = models.PositiveIntegerField(default=1)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(null=True, blank=True, help_text="Expiration time for this specific item")
    subscription_status = models.CharField(
        max_length=20,
        choices=Purchase.SubscriptionStatus.choices,
        default=Purchase.SubscriptionStatus.ONETIME,
        help_text="Subscription status for this specific item"
    )

    class Meta:
        unique_together = ('purchase', 'item')

    def get_command_execution_state(self):
        """
        Get the overall command execution state for this purchase item.
        Returns: 'pending', 'running', 'successful', 'failed'
        """
        command_jobs = self.command_jobs.all()
        if not command_jobs.exists():
            return 'pending'

        # Check if any failed
        if command_jobs.filter(state=CommandJob.State.FAILED).exists():
            return 'failed'

        # Check if any running
        if command_jobs.filter(state=CommandJob.State.RUNNING).exists():
            return 'running'

        # Check if any pending
        if command_jobs.filter(state=CommandJob.State.PENDING).exists():
            return 'pending'

        # All must be successful
        if command_jobs.filter(state=CommandJob.State.SUCCESSFUL).count() == command_jobs.count():
            return 'successful'

        return 'pending'

    def __str__(self):
        return f"{self.purchase.minecraft_username} - {self.item.name} x{self.quantity}"


class CommandJob(models.Model):
    """Model to track individual command execution jobs"""

    class State(models.TextChoices):
        PENDING = "pending", "Pending"
        RUNNING = "running", "Running"
        SUCCESSFUL = "successful", "Successful"
        FAILED = "failed", "Failed"

    purchase_item = models.ForeignKey(PurchaseItem, on_delete=models.CASCADE, related_name='command_jobs')
    command_text = models.TextField(help_text="The actual command to execute")
    sequence_order = models.PositiveIntegerField(help_text="Order in which this command should be executed")
    state = models.CharField(max_length=20, choices=State.choices, default=State.PENDING)
    created_at = models.DateTimeField(auto_now_add=True)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    error_message = models.TextField(blank=True, null=True)
    django_q_task_id = models.CharField(max_length=100, blank=True, null=True, help_text="Django-Q task ID")

    class Meta:
        ordering = ['sequence_order']
        unique_together = ('purchase_item', 'sequence_order')

    def __str__(self):
        return f"Command {self.sequence_order} for {self.purchase_item} - {self.state}"


class PlayerCountSnapshot(models.Model):
    """Model to store periodic snapshots of server player counts"""

    server = models.ForeignKey(MinecraftServer, on_delete=models.CASCADE, related_name='player_snapshots')
    timestamp = models.DateTimeField(auto_now_add=True)
    online_players = models.PositiveIntegerField(help_text="Number of players currently online")
    max_players = models.PositiveIntegerField(help_text="Maximum number of players allowed")
    query_successful = models.BooleanField(default=True, help_text="Whether the server query was successful")
    error_message = models.TextField(blank=True, null=True, help_text="Error message if query failed")

    class Meta:
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['server', '-timestamp']),
            models.Index(fields=['-timestamp']),
        ]

    def __str__(self):
        if self.query_successful:
            return f"{self.server.name}: {self.online_players}/{self.max_players} at {self.timestamp.strftime('%Y-%m-%d %H:%M')}"
        else:
            return f"{self.server.name}: Query failed at {self.timestamp.strftime('%Y-%m-%d %H:%M')}"
