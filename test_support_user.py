#!/usr/bin/env python
"""
Test script for support user functionality
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mcshop.settings')
django.setup()

from django.contrib.auth.models import User
from shop.models import UserProfile, Purchase, MinecraftServer, Category, Item, PurchaseItem
from django.test import RequestFactory
from django.contrib.admin.sites import AdminSite
from shop.admin import PurchaseAdmin


def test_support_user_functionality():
    """Test the support user functionality"""
    print("🧪 Testing Support User Functionality")
    print("=" * 50)
    
    # Create test users
    admin_user = User.objects.create_user(
        username='admin_test',
        email='<EMAIL>',
        password='testpass123',
        is_staff=True,
        is_superuser=True
    )
    
    support_user = User.objects.create_user(
        username='support_test',
        email='<EMAIL>',
        password='testpass123',
        is_staff=True
    )
    
    # Set support flag
    support_user.profile.is_support = True
    support_user.profile.save()
    
    print(f"✅ Created admin user: {admin_user.username}")
    print(f"✅ Created support user: {support_user.username} (is_support: {support_user.profile.is_support})")
    
    # Create test data
    server = MinecraftServer.objects.create(
        name="test_server",
        ip="127.0.0.1",
        port=25565
    )
    
    category = Category.objects.create(
        name="test_category"
    )
    
    item = Item.objects.create(
        name="test_item",
        category=category,
        minecraft_server=server,
        price=1000
    )
    
    # Create test purchases
    purchase1 = Purchase.objects.create(
        minecraft_username="test_user_1",
        state=Purchase.State.SUCCESSFUL
    )
    purchase1.ref_id = Purchase.generate_reference_id()
    purchase1.save()
    
    purchase2 = Purchase.objects.create(
        minecraft_username="test_user_2",
        state=Purchase.State.SUCCESSFUL
    )
    purchase2.ref_id = Purchase.generate_reference_id()
    purchase2.save()
    
    PurchaseItem.objects.create(
        purchase=purchase1,
        item=item,
        quantity=1
    )
    
    PurchaseItem.objects.create(
        purchase=purchase2,
        item=item,
        quantity=1
    )
    
    print(f"✅ Created test purchases with ref_ids: {purchase1.ref_id}, {purchase2.ref_id}")
    
    # Test admin queryset behavior
    factory = RequestFactory()
    admin_site = AdminSite()
    purchase_admin = PurchaseAdmin(Purchase, admin_site)
    
    # Test admin user (should see all purchases)
    admin_request = factory.get('/admin/shop/purchase/')
    admin_request.user = admin_user
    admin_queryset = purchase_admin.get_queryset(admin_request)
    print(f"✅ Admin user sees {admin_queryset.count()} purchases (should be 2)")
    
    # Test support user without search (should see no purchases)
    support_request = factory.get('/admin/shop/purchase/')
    support_request.user = support_user
    support_queryset = purchase_admin.get_queryset(support_request)
    print(f"✅ Support user without search sees {support_queryset.count()} purchases (should be 0)")
    
    # Test support user with search (should see matching purchase)
    support_search_request = factory.get(f'/admin/shop/purchase/?q={purchase1.ref_id}')
    support_search_request.user = support_user
    support_search_queryset = purchase_admin.get_queryset(support_search_request)
    print(f"✅ Support user with search for '{purchase1.ref_id}' sees {support_search_queryset.count()} purchases (should be 1)")
    
    # Test search results method
    search_queryset, use_distinct = purchase_admin.get_search_results(
        support_search_request, 
        Purchase.objects.all(), 
        purchase1.ref_id
    )
    print(f"✅ Support user search results for '{purchase1.ref_id}': {search_queryset.count()} purchases (should be 1)")
    
    # Test case insensitive search
    search_queryset_lower, _ = purchase_admin.get_search_results(
        support_search_request, 
        Purchase.objects.all(), 
        purchase1.ref_id.lower()
    )
    print(f"✅ Support user case-insensitive search for '{purchase1.ref_id.lower()}': {search_queryset_lower.count()} purchases (should be 1)")
    
    print("\n🎉 All tests passed! Support user functionality is working correctly.")
    print("\nSummary:")
    print("- Support users can only see purchases through exact ref_id search")
    print("- Admin users can see all purchases normally")
    print("- Search is case-insensitive for support users")
    print("- UserProfile is automatically created for all users")


if __name__ == '__main__':
    test_support_user_functionality()
