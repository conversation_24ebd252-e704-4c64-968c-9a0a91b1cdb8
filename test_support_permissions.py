#!/usr/bin/env python
"""
Test script for support user permissions and functionality
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mcshop.settings')
django.setup()

from django.contrib.auth.models import User
from shop.models import UserProfile, Purchase, MinecraftServer, Category, Item, PurchaseItem
from django.test import RequestFactory
from django.contrib.admin.sites import AdminSite
from shop.admin import PurchaseAdmin


def test_support_user_permissions():
    """Test the support user permissions and functionality"""
    print("🧪 Testing Support User Permissions & Functionality")
    print("=" * 60)
    
    # Create test users
    admin_user = User.objects.create_user(
        username='admin_test_2',
        email='<EMAIL>',
        password='testpass123',
        is_staff=True,
        is_superuser=True
    )
    
    support_user = User.objects.create_user(
        username='support_test_2',
        email='<EMAIL>',
        password='testpass123',
        is_staff=True
    )
    
    # Set support flag
    support_user.profile.is_support = True
    support_user.profile.save()
    
    print(f"✅ Created admin user: {admin_user.username}")
    print(f"✅ Created support user: {support_user.username} (is_support: {support_user.profile.is_support})")
    
    # Create test data
    server = MinecraftServer.objects.create(
        name="test_server_2",
        ip="127.0.0.1",
        port=25565
    )
    
    category = Category.objects.create(
        name="test_category_2"
    )
    
    item = Item.objects.create(
        name="test_item_2",
        category=category,
        minecraft_server=server,
        price=1000
    )
    
    # Create test purchase
    purchase = Purchase.objects.create(
        minecraft_username="test_user_permissions",
        state=Purchase.State.SUCCESSFUL
    )
    purchase.ref_id = Purchase.generate_reference_id()
    purchase.save()
    
    PurchaseItem.objects.create(
        purchase=purchase,
        item=item,
        quantity=1
    )
    
    print(f"✅ Created test purchase with ref_id: {purchase.ref_id}")
    
    # Test admin permissions
    factory = RequestFactory()
    admin_site = AdminSite()
    purchase_admin = PurchaseAdmin(Purchase, admin_site)
    
    # Test support user permissions
    print("\n🔐 Testing Support User Permissions:")
    
    # Test list view permission
    list_request = factory.get('/admin/shop/purchase/')
    list_request.user = support_user
    has_view_list = purchase_admin.has_view_permission(list_request)
    print(f"✅ Support user has list view permission: {has_view_list} (should be True)")
    
    # Test detail view permission
    detail_request = factory.get(f'/admin/shop/purchase/{purchase.id}/change/')
    detail_request.user = support_user
    has_view_detail = purchase_admin.has_view_permission(detail_request, purchase)
    print(f"✅ Support user has detail view permission: {has_view_detail} (should be True)")
    
    # Test change permission
    has_change = purchase_admin.has_change_permission(detail_request, purchase)
    print(f"✅ Support user has change permission: {has_change} (should be True)")
    
    # Test queryset behavior
    print("\n📋 Testing Queryset Behavior:")
    
    # Without search - should see nothing
    empty_request = factory.get('/admin/shop/purchase/')
    empty_request.user = support_user
    empty_queryset = purchase_admin.get_queryset(empty_request)
    print(f"✅ Support user without search sees {empty_queryset.count()} purchases (should be 0)")
    
    # With search - should see the purchase
    search_request = factory.get(f'/admin/shop/purchase/?q={purchase.ref_id}')
    search_request.user = support_user
    search_queryset = purchase_admin.get_queryset(search_request)
    print(f"✅ Support user with search sees {search_queryset.count()} purchases (should be 1)")
    
    # Test admin user (should see all)
    admin_request = factory.get('/admin/shop/purchase/')
    admin_request.user = admin_user
    admin_queryset = purchase_admin.get_queryset(admin_request)
    print(f"✅ Admin user sees {admin_queryset.count()} purchases (should be > 0)")
    
    # Test search functionality
    print("\n🔍 Testing Search Functionality:")
    
    search_queryset, use_distinct = purchase_admin.get_search_results(
        search_request, 
        Purchase.objects.all(), 
        purchase.ref_id
    )
    print(f"✅ Support user search results: {search_queryset.count()} purchases (should be 1)")
    
    # Test case insensitive search
    search_queryset_lower, _ = purchase_admin.get_search_results(
        search_request, 
        Purchase.objects.all(), 
        purchase.ref_id.lower()
    )
    print(f"✅ Case-insensitive search: {search_queryset_lower.count()} purchases (should be 1)")
    
    print("\n🎉 All permission tests passed!")
    print("\nSummary:")
    print("- Support users have proper view and change permissions")
    print("- Support users can only see purchases through exact ref_id search")
    print("- Admin users maintain full access")
    print("- Search is case-insensitive")
    print("- UserProfile approach is working correctly")


if __name__ == '__main__':
    test_support_user_permissions()
