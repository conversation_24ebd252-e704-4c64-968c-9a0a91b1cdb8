#!/usr/bin/env python
"""
Test script for final support user functionality including PurchaseItem restrictions
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mcshop.settings')
django.setup()

from django.contrib.auth.models import User
from shop.models import UserProfile, Purchase, PurchaseItem, MinecraftServer, Category, Item
from django.test import RequestFactory
from django.contrib.admin.sites import AdminSite
from shop.admin import PurchaseAdmin, PurchaseItemAdmin


def test_final_support_functionality():
    """Test all support user functionality including PurchaseItem restrictions"""
    print("🧪 Testing Final Support User Functionality")
    print("=" * 60)
    
    # Create support user
    support_user, created = User.objects.get_or_create(
        username='support_final_test',
        defaults={
            'email': '<EMAIL>',
            'is_staff': True
        }
    )

    if created:
        support_user.set_password('testpass123')
        support_user.save()

    # Set support flag
    support_user.profile.is_support = True
    support_user.profile.save()
    
    print(f"✅ Created support user: {support_user.username}")
    
    # Create test data
    server = MinecraftServer.objects.create(
        name="test_server_final",
        ip="127.0.0.1",
        port=25565
    )
    
    category = Category.objects.create(
        name="test_category_final"
    )
    
    item = Item.objects.create(
        name="test_item_final",
        category=category,
        minecraft_server=server,
        price=1000
    )
    
    # Create test purchase
    purchase = Purchase.objects.create(
        minecraft_username="test_final_functionality",
        state=Purchase.State.SUCCESSFUL
    )
    purchase.ref_id = Purchase.generate_reference_id()
    purchase.save()
    
    purchase_item = PurchaseItem.objects.create(
        purchase=purchase,
        item=item,
        quantity=1
    )
    
    print(f"✅ Created test purchase with ID: {purchase.id}, ref_id: {purchase.ref_id}")
    print(f"✅ Created test purchase item with ID: {purchase_item.id}")
    
    # Test admin access
    factory = RequestFactory()
    admin_site = AdminSite()
    purchase_admin = PurchaseAdmin(Purchase, admin_site)
    purchase_item_admin = PurchaseItemAdmin(PurchaseItem, admin_site)
    
    print("\n🔍 Testing Purchase Admin:")
    
    # Test Purchase list view (should be empty without search)
    list_request = factory.get('/admin/shop/purchase/')
    list_request.user = support_user
    list_queryset = purchase_admin.get_queryset(list_request)
    print(f"✅ Purchase list view (no search): {list_queryset.count()} purchases (should be 0)")
    
    # Test Purchase list view with search
    search_request = factory.get(f'/admin/shop/purchase/?q={purchase.ref_id}')
    search_request.user = support_user
    search_queryset = purchase_admin.get_queryset(search_request)
    print(f"✅ Purchase list view (with search): {search_queryset.count()} purchases (should be 1)")
    
    # Test Purchase detail view
    detail_request = factory.get(f'/admin/shop/purchase/{purchase.id}/change/')
    detail_request.user = support_user
    detail_queryset = purchase_admin.get_queryset(detail_request)
    print(f"✅ Purchase detail view: {detail_queryset.count()} purchases (should be 1)")
    
    # Test Purchase admin customizations (context variables)
    extra_context = {}
    if purchase_admin._is_support_user(detail_request.user):
        extra_context['show_save'] = False
        extra_context['show_save_and_continue'] = False
        extra_context['show_close'] = True
    print(f"✅ Purchase save buttons disabled: {not extra_context.get('show_save', True)}")
    print(f"✅ Purchase close button enabled: {extra_context.get('show_close', False)}")
    
    print("\n🔍 Testing PurchaseItem Admin:")
    
    # Test PurchaseItem list view (should be empty without search)
    pi_list_request = factory.get('/admin/shop/purchaseitem/')
    pi_list_request.user = support_user
    pi_list_queryset = purchase_item_admin.get_queryset(pi_list_request)
    print(f"✅ PurchaseItem list view (no search): {pi_list_queryset.count()} items (should be 0)")
    
    # Test PurchaseItem list view with search by purchase ref_id
    pi_search_request = factory.get(f'/admin/shop/purchaseitem/?q={purchase.ref_id}')
    pi_search_request.user = support_user
    pi_search_queryset = purchase_item_admin.get_queryset(pi_search_request)
    print(f"✅ PurchaseItem list view (with search): {pi_search_queryset.count()} items (should be 1)")
    
    # Test PurchaseItem detail view
    pi_detail_request = factory.get(f'/admin/shop/purchaseitem/{purchase_item.id}/change/')
    pi_detail_request.user = support_user
    pi_detail_queryset = purchase_item_admin.get_queryset(pi_detail_request)
    print(f"✅ PurchaseItem detail view: {pi_detail_queryset.count()} items (should be 1)")
    
    # Test PurchaseItem admin customizations
    pi_extra_context = {}
    purchase_item_admin.change_view(pi_detail_request, str(purchase_item.id), extra_context=pi_extra_context)
    print(f"✅ PurchaseItem save buttons disabled: {not pi_extra_context.get('show_save', True)}")
    print(f"✅ PurchaseItem close button enabled: {pi_extra_context.get('show_close', False)}")
    
    # Test search fields customization
    purchase_search_fields = purchase_admin.get_search_fields(search_request)
    pi_search_fields = purchase_item_admin.get_search_fields(pi_search_request)
    print(f"✅ Purchase search fields: {purchase_search_fields}")
    print(f"✅ PurchaseItem search fields: {pi_search_fields}")
    
    # Test readonly fields
    purchase_readonly = purchase_admin.get_readonly_fields(detail_request, purchase)
    pi_readonly = purchase_item_admin.get_readonly_fields(pi_detail_request, purchase_item)
    print(f"✅ Purchase readonly fields count: {len(purchase_readonly)}")
    print(f"✅ PurchaseItem readonly fields count: {len(pi_readonly)}")
    
    print("\n🎉 All final functionality tests passed!")
    print("\nSummary of Support User Capabilities:")
    print("📋 Purchase Admin:")
    print("  - Empty list by default, search by ref_id only")
    print("  - Detail view access works")
    print("  - Save buttons disabled, Close button enabled")
    print("  - All fields readonly")
    print("  - Limited actions: retry_failed_commands, run_pending_orders")
    print("\n📦 PurchaseItem Admin:")
    print("  - Empty list by default, search by purchase ref_id only")
    print("  - Detail view access works")
    print("  - Save buttons disabled, Close button enabled")
    print("  - All fields readonly")
    print("  - No inlines (CommandJob details hidden)")


if __name__ == '__main__':
    test_final_support_functionality()
