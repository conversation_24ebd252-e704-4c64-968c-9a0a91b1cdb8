#!/usr/bin/env python
"""
Test script for support user restrictions and customizations
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mcshop.settings')
django.setup()

from django.contrib.auth.models import User
from shop.models import UserProfile, Purchase, MinecraftServer, Category, Item, PurchaseItem
from django.test import RequestFactory
from django.contrib.admin.sites import AdminSite
from shop.admin import PurchaseAdmin


def test_support_user_restrictions():
    """Test the support user restrictions and customizations"""
    print("🧪 Testing Support User Restrictions & Customizations")
    print("=" * 60)
    
    # Create test users
    admin_user = User.objects.create_user(
        username='admin_test_3',
        email='<EMAIL>',
        password='testpass123',
        is_staff=True,
        is_superuser=True
    )
    
    support_user = User.objects.create_user(
        username='support_test_3',
        email='<EMAIL>',
        password='testpass123',
        is_staff=True
    )
    
    # Set support flag
    support_user.profile.is_support = True
    support_user.profile.save()
    
    print(f"✅ Created admin user: {admin_user.username}")
    print(f"✅ Created support user: {support_user.username} (is_support: {support_user.profile.is_support})")
    
    # Create test data
    server = MinecraftServer.objects.create(
        name="test_server_3",
        ip="127.0.0.1",
        port=25565
    )
    
    category = Category.objects.create(
        name="test_category_3"
    )
    
    item = Item.objects.create(
        name="test_item_3",
        category=category,
        minecraft_server=server,
        price=1000
    )
    
    # Create test purchase
    purchase = Purchase.objects.create(
        minecraft_username="test_user_restrictions",
        state=Purchase.State.SUCCESSFUL
    )
    purchase.ref_id = Purchase.generate_reference_id()
    purchase.save()
    
    PurchaseItem.objects.create(
        purchase=purchase,
        item=item,
        quantity=1
    )
    
    print(f"✅ Created test purchase with ref_id: {purchase.ref_id}")
    
    # Test admin customizations
    factory = RequestFactory()
    admin_site = AdminSite()
    purchase_admin = PurchaseAdmin(Purchase, admin_site)
    
    # Test support user customizations
    print("\n🎨 Testing Support User Customizations:")
    
    # Test list display
    support_request = factory.get('/admin/shop/purchase/')
    support_request.user = support_user
    
    admin_request = factory.get('/admin/shop/purchase/')
    admin_request.user = admin_user
    
    support_list_display = purchase_admin.get_list_display(support_request)
    admin_list_display = purchase_admin.get_list_display(admin_request)
    
    print(f"✅ Support user list_display: {support_list_display}")
    print(f"✅ Admin user list_display: {admin_list_display}")
    print(f"   Support has fewer fields: {len(support_list_display) < len(admin_list_display)}")
    
    # Test list filters
    support_list_filter = purchase_admin.get_list_filter(support_request)
    admin_list_filter = purchase_admin.get_list_filter(admin_request)
    
    print(f"✅ Support user list_filter: {support_list_filter}")
    print(f"✅ Admin user list_filter: {admin_list_filter}")
    print(f"   Support has fewer filters: {len(support_list_filter) < len(admin_list_filter)}")
    
    # Test search fields
    support_search_fields = purchase_admin.get_search_fields(support_request)
    admin_search_fields = purchase_admin.get_search_fields(admin_request)
    
    print(f"✅ Support user search_fields: {support_search_fields}")
    print(f"✅ Admin user search_fields: {admin_search_fields}")
    print(f"   Support has fewer search fields: {len(support_search_fields) < len(admin_search_fields)}")
    
    # Test actions
    support_actions = purchase_admin.get_actions(support_request)
    admin_actions = purchase_admin.get_actions(admin_request)
    
    print(f"✅ Support user actions: {list(support_actions.keys())}")
    print(f"✅ Admin user actions: {list(admin_actions.keys())}")
    print(f"   Support has fewer actions: {len(support_actions) < len(admin_actions)}")
    
    # Test fieldsets
    support_fieldsets = purchase_admin.get_fieldsets(support_request, purchase)
    admin_fieldsets = purchase_admin.get_fieldsets(admin_request, purchase)
    
    print(f"✅ Support user fieldsets: {[fs[0] for fs in support_fieldsets]}")
    print(f"✅ Admin user fieldsets: {[fs[0] for fs in admin_fieldsets]}")
    
    # Test readonly fields
    support_readonly = purchase_admin.get_readonly_fields(support_request, purchase)
    admin_readonly = purchase_admin.get_readonly_fields(admin_request, purchase)
    
    print(f"✅ Support user readonly fields: {support_readonly}")
    print(f"✅ Admin user readonly fields: {admin_readonly}")
    print(f"   Support has more readonly fields: {len(support_readonly) > len(admin_readonly)}")
    
    # Test inlines
    support_inlines = purchase_admin.get_inlines(support_request, purchase)
    admin_inlines = purchase_admin.get_inlines(admin_request, purchase)
    
    print(f"✅ Support user inlines: {support_inlines}")
    print(f"✅ Admin user inlines: {admin_inlines}")
    print(f"   Support has no inlines: {len(support_inlines) == 0}")
    
    print("\n🎉 All restriction tests passed!")
    print("\nSummary of Support User Restrictions:")
    print("- List Display: ref_id, items, minecraft_username, state, derived_state, created_at, payment_succeeded_at, referrer")
    print("- List Filters: created_at, payment_succeeded_at")
    print("- Search Fields: ref_id, mobile_number")
    print("- Actions: retry_failed_commands, run_pending_orders only")
    print("- Fieldsets: Purchase Info, Payment Info (zarinpal_code only), Referral Info (referrer only)")
    print("- All fields are readonly")
    print("- No inlines (PurchaseItem details hidden)")


if __name__ == '__main__':
    test_support_user_restrictions()
